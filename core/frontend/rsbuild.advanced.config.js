// 高级 Rsbuild 配置 - 包含 SVG 处理和静态文件复制
// 这个文件包含了更复杂的配置，在基础迁移完成后可以逐步应用

const { defineConfig } = require('@rsbuild/core');
const { pluginVue2 } = require('@rsbuild/plugin-vue2');
const { pluginSass } = require('@rsbuild/plugin-sass');
const { pluginLess } = require('@rsbuild/plugin-less');
const path = require('path');

const pkg = require('./package.json');
const defaultSettings = require('./src/settings.js');

const name = defaultSettings.title || 'vue Admin Template';
const port = process.env.port || process.env.npm_config_port || 9528;

module.exports = defineConfig({
  plugins: [
    pluginVue2(),
    pluginSass({
      sassLoaderOptions: {
        additionalData: `@import "@/style/index.scss";`,
        silenceDeprecations: [
          'legacy-js-api', 
          'function-units', 
          'import', 
          'global-builtin', 
          'slash-div', 
          'bogus-combinators'
        ]
      }
    }),
    pluginLess()
  ],

  // 多页面应用配置
  environments: {
    web: {
      source: {
        entry: {
          index: './src/main.js'
        }
      },
      html: {
        template: './public/index.html',
        title: name,
        filename: 'index.html'
      },
      output: {
        target: 'web'
      }
    },
    mobile: {
      source: {
        entry: {
          mobile: './src/mobile/main.js'
        }
      },
      html: {
        template: './public/mobile.html',
        title: name,
        filename: 'mobile.html'
      },
      output: {
        target: 'web'
      }
    }
  },

  source: {
    alias: {
      '@': path.resolve(__dirname, 'src')
    }
  },

  server: {
    port: port,
    open: true,
    host: 'localhost'
  },

  dev: {
    assetPrefix: process.env.VUE_CONTEXT_PATH || '/'
  },

  output: {
    assetPrefix: process.env.VUE_CONTEXT_PATH || '/',
    distPath: {
      root: 'dist'
    },
    sourceMap: {
      js: process.env.NODE_ENV === 'development' ? 'cheap-module-source-map' : false,
      css: false
    },
    filenameHash: process.env.NODE_ENV === 'production',
    filename: {
      js: process.env.NODE_ENV === 'production' 
        ? `js/[name].[contenthash:8].${pkg.version}.js`
        : 'js/[name].js',
      css: process.env.NODE_ENV === 'production'
        ? `css/[name].[contenthash:8].${pkg.version}.css`
        : 'css/[name].css'
    }
  },

  tools: {
    rspack: (config, { env }) => {
      config.name = name;

      // 复制静态文件
      const CopyPlugin = require('copy-webpack-plugin');
      config.plugins = config.plugins || [];
      config.plugins.push(
        new CopyPlugin([
          {
            from: path.join(__dirname, 'static'),
            to: path.join(__dirname, 'dist/static')
          }
        ])
      );

      // SVG 处理配置
      config.module = config.module || {};
      config.module.rules = config.module.rules || [];
      
      // 移除默认 SVG 规则
      config.module.rules = config.module.rules.filter(rule => {
        if (rule.test && rule.test.toString().includes('svg')) {
          return false;
        }
        return true;
      });

      // 添加 SVG sprite loader 配置
      config.module.rules.push(
        {
          test: /\.svg$/,
          include: [path.resolve(__dirname, 'src/icons')],
          use: [
            {
              loader: 'svg-sprite-loader',
              options: {
                symbolId: 'icon-[name]'
              }
            }
          ]
        },
        {
          test: /\.svg$/,
          include: [path.resolve(__dirname, 'src/deicons')],
          use: [
            {
              loader: 'svg-sprite-loader',
              options: {
                symbolId: '[name]'
              }
            }
          ]
        }
      );

      // DLL 配置（可选）
      if (env === 'production') {
        try {
          const webpack = require('webpack');
          const AddAssetHtmlPlugin = require('add-asset-html-webpack-plugin');
          const manifestPath = path.resolve(__dirname, './public/vendor/vendor-manifest.json');
          
          // 检查 manifest 文件是否存在
          if (require('fs').existsSync(manifestPath)) {
            config.plugins.push(
              new webpack.DllReferencePlugin({
                context: process.cwd(),
                manifest: require(manifestPath)
              }),
              new AddAssetHtmlPlugin({
                filepath: path.resolve(__dirname, './public/vendor/*.js'),
                publicPath: './vendor',
                outputPath: './vendor'
              })
            );
          }
        } catch (error) {
          console.warn('DLL 配置跳过:', error.message);
        }
      }

      return config;
    },

    postcss: {
      plugins: {
        autoprefixer: {}
      }
    }
  },

  performance: {
    buildCache: true,
    removeConsole: process.env.NODE_ENV === 'production'
  },

  security: {
    nonce: false
  }
});
