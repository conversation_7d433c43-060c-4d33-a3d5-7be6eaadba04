const { defineConfig } = require('@rsbuild/core')
const { pluginVue2 } = require('@rsbuild/plugin-vue2')
const { pluginSass } = require('@rsbuild/plugin-sass')
const { pluginLess } = require('@rsbuild/plugin-less')
const path = require('path')

const pkg = require('./package.json')
const defaultSettings = require('./src/settings.js')

const name = defaultSettings.title || 'vue Admin Template'
const port = process.env.port || process.env.npm_config_port || 9528

module.exports = defineConfig({
  plugins: [
    pluginVue2({
      vueLoaderOptions: {
        compilerOptions: {
          preserveWhitespace: false
        }
      }
    }),
    pluginSass({
      sassLoaderOptions: {
        // 暂时移除 additionalData，避免路径问题
        // additionalData: `@import "@/style/index.scss";`,
        // 处理 Sass 弃用警告
        silenceDeprecations: [
          'legacy-js-api',
          'function-units',
          'import',
          'global-builtin',
          'slash-div',
          'bogus-combinators'
        ]
      }
    }),
    pluginLess()
  ],

  // 多页面应用配置 - 使用 environments 方式
  environments: {
    web: {
      source: {
        entry: {
          index: './src/main.js'
        }
      },
      html: {
        template: './public/index.html',
        title: name,
        filename: 'index.html',
        templateParameters: {
          BASE_URL: process.env.VUE_CONTEXT_PATH || '/'
        }
      },
      output: {
        target: 'web'
      }
    },
    mobile: {
      source: {
        entry: {
          mobile: './src/mobile/main.js'
        }
      },
      html: {
        template: './public/mobile.html',
        title: name,
        filename: 'mobile.html',
        templateParameters: {
          BASE_URL: process.env.VUE_CONTEXT_PATH || '/'
        }
      },
      output: {
        target: 'web'
      }
    }
  },

  // 全局源码配置
  source: {
    // 其他源码配置
  },

  // 开发服务器配置
  server: {
    port: port,
    open: true,
    host: 'localhost'
  },

  // 开发配置
  dev: {
    // 开发环境资源前缀
    assetPrefix: process.env.VUE_CONTEXT_PATH || '/'
  },

  // 输出配置
  output: {
    // 生产环境资源前缀
    assetPrefix: process.env.VUE_CONTEXT_PATH || '/',
    // 输出目录
    distPath: {
      root: 'dist'
    },
    // 生产环境禁用 source map
    sourceMap: {
      js: process.env.NODE_ENV === 'development' ? 'cheap-module-source-map' : false,
      css: false
    },
    // 自定义文件名（包含版本号）
    filenameHash: process.env.NODE_ENV === 'production',
    filename: {
      js: process.env.NODE_ENV === 'production'
        ? `js/[name].[contenthash:8].${pkg.version}.js`
        : 'js/[name].js',
      css: process.env.NODE_ENV === 'production'
        ? `css/[name].[contenthash:8].${pkg.version}.css`
        : 'css/[name].css'
    }
  },

  // 解析配置
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src')
    }
  },

  // 工具配置
  tools: {
    // Rspack 配置（替代 webpack 配置）
    rspack: (config) => {
      // 设置应用名称
      config.name = name

      // 注意：复制静态文件和 SVG 处理将在后续步骤中配置
      // 这里先保持基础配置，避免复杂的插件依赖问题

      return config
    },

    // PostCSS 配置
    postcss: {
      plugins: {
        autoprefixer: {}
      }
    }
  },

  // 性能配置
  performance: {
    // 启用构建缓存
    buildCache: true,
    // 移除 console
    removeConsole: process.env.NODE_ENV === 'production'
  },

  // 安全配置
  security: {
    nonce: false
  }
})
