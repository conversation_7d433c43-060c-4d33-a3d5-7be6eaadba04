// 简化的 Rsbuild 配置 - 用于测试基本功能
const { defineConfig } = require('@rsbuild/core');
const { pluginVue2 } = require('@rsbuild/plugin-vue2');
const path = require('path');

const pkg = require('./package.json');
const defaultSettings = require('./src/settings.js');

const name = defaultSettings.title || 'vue Admin Template';
const port = process.env.port || process.env.npm_config_port || 9528;

module.exports = defineConfig({
  plugins: [
    pluginVue2({
      vueLoaderOptions: {
        compilerOptions: {
          preserveWhitespace: false
        }
      }
    })
  ],

  // 单页面应用配置（简化版）
  source: {
    entry: {
      index: './src/main.js'
    },
    alias: {
      '@': path.resolve(__dirname, 'src')
    }
  },

  // HTML 配置
  html: {
    template: './public/index.html',
    title: name,
    templateParameters: {
      BASE_URL: process.env.VUE_CONTEXT_PATH || '/'
    }
  },

  // 开发服务器配置
  server: {
    port: port,
    open: true,
    host: 'localhost'
  },

  // 开发配置
  dev: {
    assetPrefix: process.env.VUE_CONTEXT_PATH || '/'
  },

  // 输出配置
  output: {
    assetPrefix: process.env.VUE_CONTEXT_PATH || '/',
    distPath: {
      root: 'dist'
    },
    sourceMap: {
      js: process.env.NODE_ENV === 'development' ? 'cheap-module-source-map' : false,
      css: false
    }
  },

  // 工具配置
  tools: {
    rspack: (config) => {
      config.name = name;
      return config;
    }
  },

  // 性能配置
  performance: {
    buildCache: true
  }
});
